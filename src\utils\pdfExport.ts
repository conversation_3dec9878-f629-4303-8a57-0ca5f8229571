import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

interface ExportToPDFOptions {
  element: HTMLElement;
  filename: string;
  title?: string;
  quality?: number;
  pageFormat?: 'a4' | 'a3' | 'letter' | 'auto';
  orientation?: 'portrait' | 'landscape';
}

// 等待所有图片和异步内容加载完成
const waitForContent = async (element: HTMLElement): Promise<void> => {
  // 等待图片加载
  const images = element.querySelectorAll('img');
  const imagePromises = Array.from(images).map((img) => {
    if (img.complete) return Promise.resolve();
    return new Promise((resolve) => {
      img.onload = () => resolve(void 0);
      img.onerror = () => resolve(void 0);
    });
  });

  // 等待图表渲染完成（ECharts等）
  await new Promise((resolve) => setTimeout(resolve, 1000)); // 给图表渲染留出时间

  await Promise.all(imagePromises);

  // 额外等待确保所有内容都渲染完成
  await new Promise((resolve) => setTimeout(resolve, 500));
};

// 预处理元素样式，确保内容完整显示
const preprocessElement = (element: HTMLElement): (() => void) => {
  const originalStyles: Array<{ element: HTMLElement; property: string; value: string }> = [];

  // 保存并修改可能影响渲染的样式
  const processElement = (el: HTMLElement) => {
    // 确保元素可见
    if (el.style.display === 'none') {
      originalStyles.push({ element: el, property: 'display', value: el.style.display });
      el.style.display = 'block';
    }

    // 移除可能导致截断的样式
    if (el.style.overflow === 'hidden') {
      originalStyles.push({ element: el, property: 'overflow', value: el.style.overflow });
      el.style.overflow = 'visible';
    }

    // 确保高度自适应
    if (el.style.height && el.style.height !== 'auto') {
      originalStyles.push({ element: el, property: 'height', value: el.style.height });
      el.style.height = 'auto';
    }

    // 处理子元素
    Array.from(el.children).forEach((child) => {
      if (child instanceof HTMLElement) {
        processElement(child);
      }
    });
  };

  processElement(element);

  // 返回恢复函数
  return () => {
    originalStyles.forEach(({ element, property, value }) => {
      (element.style as any)[property] = value;
    });
  };
};

export const exportToPDF = async ({
  element,
  filename,
  title,
  quality = 2,
  pageFormat = 'a4',
  orientation = 'portrait',
}: ExportToPDFOptions): Promise<boolean> => {
  try {
    // 等待内容加载完成
    await waitForContent(element);

    // 预处理元素样式
    const restoreStyles = preprocessElement(element);

    // 等待样式应用
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 获取元素的实际尺寸
    const scrollHeight = element.scrollHeight;
    const scrollWidth = element.scrollWidth;

    // 使用更高的质量和更好的配置
    const canvas = await html2canvas(element, {
      scale: quality,
      useCORS: true,
      allowTaint: true,
      logging: false,
      backgroundColor: '#ffffff',
      width: scrollWidth,
      height: scrollHeight,
      scrollX: 0,
      scrollY: 0,
      windowWidth: scrollWidth,
      windowHeight: scrollHeight,
      onclone: (clonedDoc) => {
        // 确保克隆的文档中所有内容都可见
        const clonedElement = clonedDoc.querySelector('[data-html2canvas-clone]');
        if (clonedElement) {
          (clonedElement as HTMLElement).style.overflow = 'visible';
          (clonedElement as HTMLElement).style.height = 'auto';
        }
      },
    });

    // 恢复原始样式
    restoreStyles();

    const imgData = canvas.toDataURL('image/jpeg', 0.95);

    // 定义页面尺寸（毫米）
    const pageFormats = {
      a4: { width: 210, height: 297 },
      a3: { width: 297, height: 420 },
      letter: { width: 216, height: 279 },
      auto: { width: 210, height: 297 }, // 默认A4，后面会调整
    };

    let pageSize = pageFormats[pageFormat];

    // 如果是自动模式，根据内容调整页面大小
    if (pageFormat === 'auto') {
      const pxToMm = (px: number) => px * 0.264583;
      const contentWidth = pxToMm(canvas.width);
      const contentHeight = pxToMm(canvas.height);

      // 设置合理的最大尺寸
      const maxWidth = 400; // A3宽度左右
      const maxHeight = 600; // 比A3高一些

      pageSize = {
        width: Math.min(contentWidth + 20, maxWidth), // 加上边距
        height: Math.min(contentHeight + 40, maxHeight), // 加上边距和标题空间
      };
    }

    const pdf = new jsPDF({
      orientation: orientation,
      unit: 'mm',
      format: [pageSize.width, pageSize.height],
    });

    const margin = 10;
    const titleOffset = title ? 30 : 0;

    // 添加标题
    if (title) {
      pdf.setFont('helvetica', 'bold');
      pdf.setFontSize(16);
      pdf.setTextColor(0, 0, 0);

      const titleWidth = pdf.getTextWidth(title);
      const titleX = (pageSize.width - titleWidth) / 2;

      pdf.text(title, titleX, margin + 15);

      // 添加分隔线
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.line(margin, margin + 20, pageSize.width - margin, margin + 20);
    }

    // 计算图片在PDF中的尺寸
    const availableWidth = pageSize.width - margin * 2;
    const availableHeight = pageSize.height - margin * 2 - titleOffset;

    const imgAspectRatio = canvas.width / canvas.height;
    const availableAspectRatio = availableWidth / availableHeight;

    let imgWidth, imgHeight;

    if (imgAspectRatio > availableAspectRatio) {
      // 图片更宽，以宽度为准
      imgWidth = availableWidth;
      imgHeight = availableWidth / imgAspectRatio;
    } else {
      // 图片更高，以高度为准
      imgHeight = availableHeight;
      imgWidth = availableHeight * imgAspectRatio;
    }

    // 居中放置图片
    const imgX = (pageSize.width - imgWidth) / 2;
    const imgY = margin + titleOffset;

    // 如果内容太高，需要分页处理
    if (imgHeight > availableHeight) {
      const pageHeight = availableHeight;
      const totalPages = Math.ceil(imgHeight / pageHeight);

      for (let page = 0; page < totalPages; page++) {
        if (page > 0) {
          pdf.addPage([pageSize.width, pageSize.height]);

          // 为后续页面添加页码
          pdf.setFontSize(10);
          pdf.setTextColor(128, 128, 128);
          pdf.text(`第 ${page + 1} 页，共 ${totalPages} 页`, pageSize.width - 40, pageSize.height - 5);
        }

        const sourceY = (canvas.height / totalPages) * page;
        const sourceHeight = Math.min(canvas.height / totalPages, canvas.height - sourceY);

        // 创建当前页的canvas片段
        const pageCanvas = document.createElement('canvas');
        const pageCtx = pageCanvas.getContext('2d')!;
        pageCanvas.width = canvas.width;
        pageCanvas.height = canvas.height / totalPages;

        pageCtx.drawImage(canvas, 0, sourceY, canvas.width, sourceHeight, 0, 0, canvas.width, pageCanvas.height);

        const pageImgData = pageCanvas.toDataURL('image/jpeg', 0.95);
        pdf.addImage(pageImgData, 'JPEG', imgX, imgY, imgWidth, pageHeight);
      }
    } else {
      pdf.addImage(imgData, 'JPEG', imgX, imgY, imgWidth, imgHeight);
    }

    pdf.save(filename);
    return true;
  } catch (error) {
    console.error('PDF导出错误:', error);
    return false;
  }
};
